/**
 * 网络状态面板组件
 * 用于显示网络状态信息和图表
 */
import React, { useState, useEffect, useRef } from 'react';
import { Card, Row, Col, Statistic, Badge, Tabs, Table, Tooltip, Button, Space, Divider, Alert, Progress } from 'antd';
import {
  ReloadOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  WarningOutlined,
  LineChartOutlined,
  AreaChartOutlined,
  DashboardOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  MinusOutlined
} from '@ant-design/icons';
import { Line, Area, Gauge, Liquid, RingProgress } from '@ant-design/charts';
import { useTranslation } from 'react-i18next';
// 移除引擎直接导入
import './NetworkStatusPanel.css';



// 网络质量等级枚举
export enum NetworkQualityLevel {
  UNKNOWN = 'unknown',
  VERY_BAD = 'very_bad',
  BAD = 'bad',
  MEDIUM = 'medium',
  GOOD = 'good',
  EXCELLENT = 'excellent',
}

// 网络问题类型枚举
export enum NetworkIssueType {
  NONE = 'none',
  HIGH_LATENCY = 'high_latency',
  PACKET_LOSS = 'packet_loss',
  HIGH_JITTER = 'high_jitter',
  LOW_BANDWIDTH = 'low_bandwidth',
  UNSTABLE_CONNECTION = 'unstable_connection',
  CONNECTION_INTERRUPTED = 'connection_interrupted',
  NETWORK_CONGESTION = 'network_congestion',
  BANDWIDTH_FLUCTUATION = 'bandwidth_fluctuation',
  SLOW_SERVER_RESPONSE = 'slow_server_response',
  DNS_RESOLUTION_ISSUE = 'dns_resolution_issue',
}

// 网络问题接口
export interface NetworkIssue {
  type: NetworkIssueType;
  severity: number;
  description: string;
  solution: string;
  startTime: number;
  duration: number;
  resolved: boolean;
}

// 网络质量数据接口
export interface NetworkQualityData {
  rtt: number;
  packetLoss: number;
  jitter: number;
  bandwidth: number;
  uploadBandwidth?: number;
  downloadBandwidth?: number;
  stability?: number;
  congestion?: number;
  level: NetworkQualityLevel;
  timestamp: number;
  issues?: NetworkIssue[];
  bandwidthUtilization?: number;
  qualityScore?: number;
  reliability?: number;
  latencyTrend?: number;
  networkType?: string;
  connectionType?: string;
  signalStrength?: number;
  hopCount?: number;
  serverResponseTime?: number;
  connectionEstablishTime?: number;
  dataTransferRate?: number;
  interfaceStatus?: string;
  dnsResolutionTime?: number;
  errorCount?: number;
  warningCount?: number;
  prediction?: {
    rtt?: number;
    packetLoss?: number;
    bandwidth?: number;
    stability?: number;
    confidence?: number;
  };
}

// 格式化带宽显示
const formatBandwidth = (bytes: number): string => {
  if (bytes < 1024) {
    return `${bytes.toFixed(0)} B/s`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(1)} KB/s`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB/s`;
  }
};

// 获取网络质量等级对应的颜色
const getQualityLevelColor = (level: NetworkQualityLevel): string => {
  switch (level) {
    case NetworkQualityLevel.EXCELLENT:
      return '#52c41a'; // 绿色
    case NetworkQualityLevel.GOOD:
      return '#1890ff'; // 蓝色
    case NetworkQualityLevel.MEDIUM:
      return '#faad14'; // 黄色
    case NetworkQualityLevel.BAD:
      return '#fa8c16'; // 橙色
    case NetworkQualityLevel.VERY_BAD:
      return '#f5222d'; // 红色
    default:
      return '#d9d9d9'; // 灰色
  }
};

// 获取网络质量等级对应的文本
const getQualityLevelText = (level: NetworkQualityLevel): string => {
  switch (level) {
    case NetworkQualityLevel.EXCELLENT:
      return '极好';
    case NetworkQualityLevel.GOOD:
      return '良好';
    case NetworkQualityLevel.MEDIUM:
      return '一般';
    case NetworkQualityLevel.BAD:
      return '较差';
    case NetworkQualityLevel.VERY_BAD:
      return '极差';
    default:
      return '未知';
  }
};

// 获取网络问题类型对应的图标
const getIssueTypeIcon = (type: NetworkIssueType) => {
  switch (type) {
    case NetworkIssueType.HIGH_LATENCY:
      return <WarningOutlined style={{ color: '#fa8c16' }} />;
    case NetworkIssueType.PACKET_LOSS:
      return <WarningOutlined style={{ color: '#f5222d' }} />;
    case NetworkIssueType.HIGH_JITTER:
      return <WarningOutlined style={{ color: '#faad14' }} />;
    case NetworkIssueType.LOW_BANDWIDTH:
      return <WarningOutlined style={{ color: '#1890ff' }} />;
    case NetworkIssueType.UNSTABLE_CONNECTION:
      return <WarningOutlined style={{ color: '#722ed1' }} />;
    case NetworkIssueType.CONNECTION_INTERRUPTED:
      return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
    default:
      return <InfoCircleOutlined />;
  }
};

// 获取网络问题类型对应的文本
const getIssueTypeText = (type: NetworkIssueType): string => {
  switch (type) {
    case NetworkIssueType.HIGH_LATENCY:
      return '高延迟';
    case NetworkIssueType.PACKET_LOSS:
      return '丢包';
    case NetworkIssueType.HIGH_JITTER:
      return '高抖动';
    case NetworkIssueType.LOW_BANDWIDTH:
      return '低带宽';
    case NetworkIssueType.UNSTABLE_CONNECTION:
      return '连接不稳定';
    case NetworkIssueType.CONNECTION_INTERRUPTED:
      return '连接中断';
    default:
      return '未知问题';
  }
};

interface NetworkStatusPanelProps {
  /** 当前网络质量数据 */
  currentQuality?: NetworkQualityData;
  /** 历史网络质量数据 */
  qualityHistory?: NetworkQualityData[];
  /** 网络问题列表 */
  issues?: NetworkIssue[];
  /** 是否连接 */
  connected?: boolean;
  /** 是否正在加载数据 */
  loading?: boolean;
  /** 刷新数据回调 */
  onRefresh?: () => void;
  /** 解决问题回调 */
  onResolveIssue?: (issue: NetworkIssue) => void;
}

/**
 * 网络状态面板组件
 */
const NetworkStatusPanel: React.FC<NetworkStatusPanelProps> = ({
  currentQuality,
  qualityHistory = [],
  issues = [],
  connected = false,
  loading = false,
  onRefresh,
  onResolveIssue}) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('overview');
  const [showDetailedMetrics, setShowDetailedMetrics] = useState(false);

  // 准备图表数据
  const prepareChartData = (field: keyof NetworkQualityData) => {
    return qualityHistory.map((data, index) => ({
      index,
      value: data[field] as number,
      time: new Date(data.timestamp).toLocaleTimeString()}));
  };

  // 渲染概览
  const renderOverview = () => {
    if (!currentQuality) {
      return (
        <Alert
          message={t('network.status.noData')}
          description={t('network.status.noDataDescription')}
          type="info"
          showIcon
        />
      );
    }

    return (
      <div className="network-overview">
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.connectionStatus')}
                value={connected ? t('network.status.connected') : t('network.status.disconnected')}
                valueStyle={{ color: connected ? '#52c41a' : '#f5222d' }}
                prefix={<Badge status={connected ? 'success' : 'error'} />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.quality')}
                value={getQualityLevelText(currentQuality.level)}
                valueStyle={{ color: getQualityLevelColor(currentQuality.level) }}
                prefix={<DashboardOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.latency')}
                value={currentQuality.rtt.toFixed(0)}
                suffix="ms"
                valueStyle={{
                  color: currentQuality.rtt > 200 ? '#f5222d' :
                         currentQuality.rtt > 100 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.packetLoss')}
                value={(currentQuality.packetLoss * 100).toFixed(1)}
                suffix="%"
                valueStyle={{
                  color: currentQuality.packetLoss > 0.1 ? '#f5222d' :
                         currentQuality.packetLoss > 0.05 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={12}>
            <Card title={t('network.status.bandwidth')}>
              <Statistic
                title={t('network.status.totalBandwidth')}
                value={formatBandwidth(currentQuality.bandwidth)}
                valueStyle={{ color: '#1890ff' }}
              />
              <Divider style={{ margin: '12px 0' }} />
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title={t('network.status.uploadBandwidth')}
                    value={formatBandwidth(currentQuality.uploadBandwidth || 0)}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title={t('network.status.downloadBandwidth')}
                    value={formatBandwidth(currentQuality.downloadBandwidth || 0)}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
          <Col span={12}>
            <Card title={t('network.status.stability')}>
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title={t('network.status.jitter')}
                    value={currentQuality.jitter.toFixed(1)}
                    suffix="ms"
                    valueStyle={{
                      color: currentQuality.jitter > 50 ? '#f5222d' :
                             currentQuality.jitter > 20 ? '#faad14' : '#52c41a'
                    }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title={t('network.status.stabilityIndex')}
                    value={(currentQuality.stability || 1).toFixed(2)}
                    valueStyle={{
                      color: (currentQuality.stability || 1) < 0.5 ? '#f5222d' :
                             (currentQuality.stability || 1) < 0.8 ? '#faad14' : '#52c41a'
                    }}
                    suffix="/1.0"
                  />
                </Col>
              </Row>
              <Divider style={{ margin: '12px 0' }} />
              <div>
                <div style={{ marginBottom: 5 }}>{t('network.status.congestion')}</div>
                <Progress
                  percent={Math.round((currentQuality.congestion || 0) * 100)}
                  status={(currentQuality.congestion || 0) > 0.7 ? 'exception' :
                          (currentQuality.congestion || 0) > 0.3 ? 'normal' : 'success'}
                  strokeColor={{
                    '0%': '#52c41a',
                    '50%': '#faad14',
                    '100%': '#f5222d'}}
                />
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染仪表盘
  const renderDashboard = () => {
    if (!currentQuality) {
      return (
        <Alert
          message={t('network.status.noData')}
          description={t('network.status.noDataDescription')}
          type="info"
          showIcon
        />
      );
    }

    // 获取延迟趋势图标
    const getLatencyTrendIcon = () => {
      if (currentQuality.latencyTrend === 1) {
        return <ArrowDownOutlined style={{ color: '#52c41a' }} />;
      } else if (currentQuality.latencyTrend === -1) {
        return <ArrowUpOutlined style={{ color: '#f5222d' }} />;
      } else {
        return <MinusOutlined style={{ color: '#1890ff' }} />;
      }
    };

    return (
      <div className="network-dashboard">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.status.qualityScore')}</h3>
                <Gauge
                  percent={(currentQuality.qualityScore || 0) / 100}
                  range={{
                    color: ['#F4664A', '#FAAD14', '#30BF78']}}
                  indicator={{
                    pointer: {
                      style: {
                        stroke: '#D0D0D0'}},
                    pin: {
                      style: {
                        stroke: '#D0D0D0'}}}}
                  statistic={{
                    content: {
                      formatter: () => `${currentQuality.qualityScore || 0}`,
                      style: {
                        fontSize: '24px',
                        lineHeight: '24px'}}}}
                />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.status.reliability')}</h3>
                <Liquid
                  percent={currentQuality.reliability || 1}
                  outline={{
                    border: 2,
                    distance: 4}}
                  wave={{
                    length: 128}}
                  statistic={{
                    content: {
                      formatter: () => `${((currentQuality.reliability || 1) * 100).toFixed(0)}%`,
                      style: {
                        fontSize: '24px'}}}}
                />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <h3>{t('network.status.congestion')}</h3>
                <RingProgress
                  percent={currentQuality.congestion || 0}
                  color={['#F4664A', '#E8EDF3']}
                  statistic={{
                    content: {
                      formatter: () => `${((currentQuality.congestion || 0) * 100).toFixed(0)}%`,
                      style: {
                        fontSize: '24px'}}}}
                />
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.latency')}
                value={currentQuality.rtt.toFixed(0)}
                suffix="ms"
                valueStyle={{
                  color: currentQuality.rtt > 200 ? '#f5222d' :
                         currentQuality.rtt > 100 ? '#faad14' : '#52c41a'
                }}
                prefix={getLatencyTrendIcon()}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.packetLoss')}
                value={(currentQuality.packetLoss * 100).toFixed(1)}
                suffix="%"
                valueStyle={{
                  color: currentQuality.packetLoss > 0.1 ? '#f5222d' :
                         currentQuality.packetLoss > 0.05 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.jitter')}
                value={currentQuality.jitter.toFixed(1)}
                suffix="ms"
                valueStyle={{
                  color: currentQuality.jitter > 50 ? '#f5222d' :
                         currentQuality.jitter > 20 ? '#faad14' : '#52c41a'
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title={t('network.status.bandwidth')}
                value={formatBandwidth(currentQuality.bandwidth)}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
        </Row>

        {showDetailedMetrics && (
          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={8}>
              <Card>
                <Statistic
                  title={t('network.status.uploadBandwidth')}
                  value={formatBandwidth(currentQuality.uploadBandwidth || 0)}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title={t('network.status.downloadBandwidth')}
                  value={formatBandwidth(currentQuality.downloadBandwidth || 0)}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card>
                <Statistic
                  title={t('network.status.bandwidthUtilization')}
                  value={((currentQuality.bandwidthUtilization || 0) * 100).toFixed(0)}
                  suffix="%"
                  valueStyle={{
                    color: (currentQuality.bandwidthUtilization || 0) > 0.9 ? '#f5222d' :
                           (currentQuality.bandwidthUtilization || 0) > 0.7 ? '#faad14' : '#52c41a'
                  }}
                />
              </Card>
            </Col>
          </Row>
        )}

        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button
            type="link"
            onClick={() => setShowDetailedMetrics(!showDetailedMetrics)}
          >
            {showDetailedMetrics ? t('network.status.hideDetailedMetrics') : t('network.status.showDetailedMetrics')}
          </Button>
        </div>
      </div>
    );
  };

  // 渲染图表
  const renderCharts = () => {
    if (qualityHistory.length < 2) {
      return (
        <Alert
          message={t('network.status.insufficientData')}
          description={t('network.status.insufficientDataDescription')}
          type="info"
          showIcon
        />
      );
    }

    const chartItems = [
      {
        key: 'latency',
        label: <span><LineChartOutlined />{t('network.status.latency')}</span>,
        children: (
          <Line
            data={prepareChartData('rtt')}
            xField="time"
            yField="value"
            point={{ size: 3 }}
            smooth
            annotations={[
              {
                type: 'line',
                start: ['min', 100],
                end: ['max', 100],
                style: {
                  stroke: '#faad14',
                  lineDash: [4, 4]}},
              {
                type: 'line',
                start: ['min', 200],
                end: ['max', 200],
                style: {
                  stroke: '#f5222d',
                  lineDash: [4, 4]}},
            ]}
            tooltip={{
              formatter: (datum) => {
                return { name: t('network.status.latency'), value: `${datum.value} ms` };
              }}}
          />
        )
      },
      {
        key: 'packetLoss',
        label: <span><LineChartOutlined />{t('network.status.packetLoss')}</span>,
        children: (
          <Line
            data={prepareChartData('packetLoss').map(item => ({ ...item, value: item.value * 100 }))}
            xField="time"
            yField="value"
            point={{ size: 3 }}
            smooth
            annotations={[
              {
                type: 'line',
                start: ['min', 5],
                end: ['max', 5],
                style: {
                  stroke: '#faad14',
                  lineDash: [4, 4]}},
              {
                type: 'line',
                start: ['min', 10],
                end: ['max', 10],
                style: {
                  stroke: '#f5222d',
                  lineDash: [4, 4]}},
            ]}
            tooltip={{
              formatter: (datum) => {
                return { name: t('network.status.packetLoss'), value: `${datum.value.toFixed(1)}%` };
              }}}
          />
        )
      },
      {
        key: 'bandwidth',
        label: <span><AreaChartOutlined />{t('network.status.bandwidth')}</span>,
        children: (
          <Area
            data={prepareChartData('bandwidth')}
            xField="time"
            yField="value"
            smooth
            tooltip={{
              formatter: (datum) => {
                return { name: t('network.status.bandwidth'), value: formatBandwidth(datum.value) };
              }}}
          />
        )
      },
      {
        key: 'qualityScore',
        label: <span><AreaChartOutlined />{t('network.status.qualityScore')}</span>,
        children: (
          <Line
            data={prepareChartData('qualityScore')}
            xField="time"
            yField="value"
            point={{ size: 3 }}
            smooth
            annotations={[
              {
                type: 'line',
                start: ['min', 60],
                end: ['max', 60],
                style: {
                  stroke: '#faad14',
                  lineDash: [4, 4]}},
              {
                type: 'line',
                start: ['min', 80],
                end: ['max', 80],
                style: {
                  stroke: '#52c41a',
                  lineDash: [4, 4]}},
            ]}
            tooltip={{
              formatter: (datum) => {
                return { name: t('network.status.qualityScore'), value: `${datum.value.toFixed(0)}` };
              }}}
          />
        )
      },
      {
        key: 'reliability',
        label: <span><LineChartOutlined />{t('network.status.reliability')}</span>,
        children: (
          <Line
            data={prepareChartData('reliability')}
            xField="time"
            yField="value"
            point={{ size: 3 }}
            smooth
            annotations={[
              {
                type: 'line',
                start: ['min', 0.7],
                end: ['max', 0.7],
                style: {
                  stroke: '#faad14',
                  lineDash: [4, 4]}},
              {
                type: 'line',
                start: ['min', 0.9],
                end: ['max', 0.9],
                style: {
                  stroke: '#52c41a',
                  lineDash: [4, 4]}},
            ]}
            tooltip={{
              formatter: (datum) => {
                return { name: t('network.status.reliability'), value: `${(datum.value * 100).toFixed(0)}%` };
              }}}
          />
        )
      },
      {
        key: 'jitter',
        label: <span><LineChartOutlined />{t('network.status.jitter')}</span>,
        children: (
          <Line
            data={prepareChartData('jitter')}
            xField="time"
            yField="value"
            point={{ size: 3 }}
            smooth
            annotations={[
              {
                type: 'line',
                start: ['min', 20],
                end: ['max', 20],
                style: {
                  stroke: '#faad14',
                  lineDash: [4, 4]}},
              {
                type: 'line',
                start: ['min', 50],
                end: ['max', 50],
                style: {
                  stroke: '#f5222d',
                  lineDash: [4, 4]}},
            ]}
            tooltip={{
              formatter: (datum) => {
                return { name: t('network.status.jitter'), value: `${datum.value.toFixed(1)} ms` };
              }}}
          />
        )
      }
    ];

    return (
      <div className="network-charts">
        <Tabs defaultActiveKey="latency" type="card" items={chartItems} />
      </div>
    );
  };

  // 渲染问题列表
  const renderIssues = () => {
    if (issues.length === 0) {
      return (
        <Alert
          message={t('network.status.noIssues')}
          description={t('network.status.noIssuesDescription')}
          type="success"
          showIcon
          icon={<CheckCircleOutlined />}
        />
      );
    }

    const columns = [
      {
        title: t('network.status.issueType'),
        dataIndex: 'type',
        key: 'type',
        render: (type: NetworkIssueType) => (
          <Space>
            {getIssueTypeIcon(type)}
            {getIssueTypeText(type)}
          </Space>
        )},
      {
        title: t('network.status.severity'),
        dataIndex: 'severity',
        key: 'severity',
        render: (severity: number) => (
          <Progress
            percent={Math.round(severity * 100)}
            size="small"
            status={severity > 0.7 ? 'exception' : 'normal'}
            strokeColor={{
              '0%': '#52c41a',
              '50%': '#faad14',
              '100%': '#f5222d'}}
          />
        )},
      {
        title: t('network.status.description'),
        dataIndex: 'description',
        key: 'description'},
      {
        title: t('network.status.solution'),
        dataIndex: 'solution',
        key: 'solution',
        render: (solution: string) => (
          <Tooltip title={solution}>
            <Button type="link">{t('network.status.viewSolution')}</Button>
          </Tooltip>
        )},
      {
        title: t('network.status.duration'),
        dataIndex: 'duration',
        key: 'duration',
        render: (duration: number) => (
          <span>{Math.round(duration / 1000)}s</span>
        )},
      {
        title: t('network.status.actions'),
        key: 'actions',
        render: (_, record: NetworkIssue) => (
          <Button
            type="primary"
            size="small"
            onClick={() => onResolveIssue && onResolveIssue(record)}
          >
            {t('network.status.resolve')}
          </Button>
        )},
    ];

    return (
      <Table
        dataSource={issues}
        columns={columns}
        rowKey={(record) => `${record.type}-${record.startTime}`}
        pagination={false}
      />
    );
  };

  return (
    <div className="network-status-panel">
      <div className="panel-header">
        <h2>{t('network.status.title')}</h2>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={onRefresh}
          loading={loading}
        >
          {t('network.status.refresh')}
        </Button>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'overview',
            label: <span><DashboardOutlined />{t('network.status.overview')}</span>,
            children: renderOverview()
          },
          {
            key: 'dashboard',
            label: <span><DashboardOutlined />{t('network.status.dashboard')}</span>,
            children: renderDashboard()
          },
          {
            key: 'charts',
            label: <span><LineChartOutlined />{t('network.status.charts')}</span>,
            children: renderCharts()
          },
          {
            key: 'issues',
            label: (
              <span>
                <WarningOutlined />
                {t('network.status.issues')}
                {issues.length > 0 && (
                  <Badge count={issues.length} style={{ marginLeft: 8 }} />
                )}
              </span>
            ),
            children: renderIssues()
          }
        ]}
      />
    </div>
  );
};

export default NetworkStatusPanel;
